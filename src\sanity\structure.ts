import type { StructureResolver } from "sanity/structure";

// https://www.sanity.io/docs/structure-builder-cheat-sheet
export const structure: StructureResolver = (S) =>
  S.list()
    .title("Conteúdo")
    .items([
      // Newsletter section
      S.listItem()
        .title("Newsletter")
        .child(
          S.list()
            .title("Newsletter")
            .items([
              S.documentTypeListItem("newsletter").title("Newsletters"),
              S.documentTypeListItem("subscriber").title("Assinantes"),
            ]),
        ),
      S.divider(),
      // Other content types
      ...S.documentTypeListItems().filter(
        (item) =>
          item.getId() &&
          !["newsletter", "subscriber", "post", "category", "author"].includes(
            item.getId()!,
          ),
      ),
    ]);
